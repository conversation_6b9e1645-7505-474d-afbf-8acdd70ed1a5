from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    TAPResult,
    UPNIdentifierArgs,
    UserIdentifierArgs,
)


class UserLoginStatus(BaseModel):
    enabled: bool = Field(
        description="Indicates whether the user's login is enabled.",
        default=False,
    )


class UserLoginResult(TAPResult[UserLoginStatus]):
    ...


user_login_metadata = IntegrationActionMetadata(
    args_type=UserIdentifierArgs,
    result_type=UserLoginResult,
)

user_login_by_upn_metadata = IntegrationActionMetadata(
    args_type=UPNIdentifierArgs,
    result_type=UserLoginResult,
)


class EnableUserLogin(IntegrationAction):
    name = "Enable user login"
    action_type = IntegrationActionType.ENABLE_USER_LOGIN
    entitlement = Entitlement.mdr
    metadata = user_login_metadata


class EnableUserLoginByUPN(IntegrationAction):
    name = "Enable user login by UPN"
    action_type = IntegrationActionType.ENABLE_USER_LOGIN_BY_UPN
    entitlement = Entitlement.mdr
    metadata = user_login_by_upn_metadata


class DisableUserLogin(IntegrationAction):
    name = "Disable user login"
    action_type = IntegrationActionType.DISABLE_USER_LOGIN
    entitlement = Entitlement.mdr
    metadata = user_login_metadata


class DisableUserLoginByUPN(IntegrationAction):
    name = "Disable user login by UPN"
    action_type = IntegrationActionType.DISABLE_USER_LOGIN_BY_UPN
    entitlement = Entitlement.mdr
    metadata = user_login_by_upn_metadata


# User locking status model (similar to UserLoginStatus but for locking)
class UserLockStatus(BaseModel):
    locked: bool = Field(
        description="Indicates whether the user account is locked.",
        default=False,
    )


class UserLockResult(TAPResult[UserLockStatus]):
    ...


user_lock_metadata = IntegrationActionMetadata(
    args_type=UserIdentifierArgs,
    result_type=UserLockResult,
)


class LockUser(IntegrationAction):
    name = "Lock user"
    action_type = "cisco_duo/lock_user"
    entitlement = Entitlement.mdr
    metadata = user_lock_metadata


class UnlockUser(IntegrationAction):
    name = "Unlock user"
    action_type = "cisco_duo/unlock_user"
    entitlement = Entitlement.mdr
    metadata = user_lock_metadata
