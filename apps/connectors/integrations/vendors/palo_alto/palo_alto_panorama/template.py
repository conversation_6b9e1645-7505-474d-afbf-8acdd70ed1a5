from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import PaloAltoPanoramaV1TemplateVersion


class PaloAltoPanoramaTemplate(Template):
    id = "palo_alto_panorama"
    name = "Palo Alto Panorama"
    category = Template.Category.NETWORK_SECURITY
    versions = {
        PaloAltoPanoramaV1TemplateVersion.id: PaloAltoPanoramaV1TemplateVersion(),
    }
    vendor = Vendors.PALO_ALTO

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
