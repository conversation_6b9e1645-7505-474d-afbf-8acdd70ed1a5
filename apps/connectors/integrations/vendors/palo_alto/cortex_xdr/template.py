from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CortexXdrV1TemplateVersion


class CortexXdrTemplate(Template):
    id = "cortex_xdr"
    name = "Palo Alto Cortex XDR"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        CortexXdrV1TemplateVersion.id: CortexXdrV1TemplateVersion(),
    }
    endpoint_coverage_available = True
    vendor = Vendors.PALO_ALTO

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
