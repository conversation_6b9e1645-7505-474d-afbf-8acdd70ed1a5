from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


class VmwareAriaV1Config(TemplateVersionConfig):
    server_url: HttpUrl = Field(
        title="Server URL",
        description="The URL of the VMware Aria server",
    )
    api_key: str = Field(
        title="API Key",
        description="The API key to use for authentication",
    )


class VmwareAriaV1Connection(ConnectionTemplate):
    id = "vmware_aria"
    name = "VMware Aria"
    config_model = VmwareAriaV1Config
