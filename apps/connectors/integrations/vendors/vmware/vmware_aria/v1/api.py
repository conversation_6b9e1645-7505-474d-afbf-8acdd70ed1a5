from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["content"]
    items_count = len(response["content"])
    total_count = 0
    while items_count > 0:
        total_count += items_count
        response = bound_method(**kwargs, skip=total_count)
        items_count = len(response["content"])
        if items_count > 0:
            yield response["content"]
        else:
            break


class VmwareAriaV1Api(ApiBase):
    def __init__(self, server_url=None, api_key=None):
        self.base_url = server_url
        self.api_key = api_key
        super().__init__(
            base_url=self.base_url,
            static_headers={
                "Accept": "application/json",
                "Authorization": self.api_key,
            },
        )

    def get_machines(self, top=1000, skip=0):
        return self.session.get(
            self.url("machines"), params={"$top": top, "$skip": skip}
        ).json()

    def get_about(self):
        return self.session.get(self.url("/iaas/api/about")).json()
