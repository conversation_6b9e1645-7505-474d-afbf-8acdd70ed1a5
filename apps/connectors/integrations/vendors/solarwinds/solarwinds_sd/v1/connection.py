from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


# enum class with url choices
class SolarwindsSdV1Url(StrEnum):
    US = "https://api.samanage.com"
    EU = "https://apieu.samanage.com"
    APJ = "https://apiau.samanage.com"


class SolarwindsSdV1Config(TemplateVersionConfig):
    url: SolarwindsSdV1Url = Field(
        title="Solarwinds  URL",
        description="The service endpoint used to connect with SolarWinds. See https://apidoc.samanage.com/",
    )
    api_token: str = Field(
        title="API Token",
        description="API Token. See https://documentation.solarwinds.com/en/success_center/swsd/content/completeguidetoswsd/token-authentication-for-api-integration.htm",
    )


class SolarwindsSdV1Connection(ConnectionTemplate):
    id = "solarwinds_sd"
    name = "Solarwinds Service Desk"
    config_model = SolarwindsSdV1Config
