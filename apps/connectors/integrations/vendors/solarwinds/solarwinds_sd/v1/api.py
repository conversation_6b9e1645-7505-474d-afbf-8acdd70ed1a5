from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response
    items_count = len(response)
    page_no = 1
    while items_count > 0:
        page_no = page_no + 1
        response = bound_method(page_no=page_no, **kwargs)
        items_count = len(response)
        if items_count > 0:
            yield response


class SolarwindsSdV1Api(ApiBase):
    """
    API client for SolarWinds Service Desk
    https://apidoc.samanage.com/
    https://documentation.solarwinds.com/en/success_center/swsd/content/completeguidetoswsd/token-authentication-for-api-integration.htm
    """

    def __init__(self, url=None, api_token=None, **kwargs):
        self.api_token = api_token

        static_headers = {
            "Accept": "application/vnd.samanage.v2.1+json",
            "Content-Type": "application/json",
            "X-Samanage-Authorization": "Bearer {api_token}",
        }
        super().__init__(base_url=url, static_headers=static_headers)

    def get_hardwares(self, page_no=1, per_page=100):
        """
        Get a list of Hardwares
        """
        query_params = {
            "per_page": per_page,
            "page": page_no,
        }
        url_path = self.url("/hardwares")
        response = self.session.get(url_path, params=query_params)

        return response.json()
