from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class CheckPointNgfwTemplate(Template):
    id = "check_point_ngfw"
    name = "Check Point NGFW"
    category = Template.Category.NETWORK_SECURITY
    versions = {}
    vendor = Vendors.CHECK_POINT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
