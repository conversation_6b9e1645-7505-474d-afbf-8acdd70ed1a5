from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class CylanceEdrTemplate(Template):
    id = "cylance_edr"
    name = "Cylance Endpoint Security"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {}
    vendor = Vendors.CYLANCE

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
