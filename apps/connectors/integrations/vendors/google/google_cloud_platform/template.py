from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class GoogleCloudPlatformTemplate(Template):
    id = "google_cloud_platform"
    name = "Google Cloud Platform"
    category = Template.Category.CLOUD_SECURITY
    versions = {}
    vendor = Vendors.GOOGLE

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
