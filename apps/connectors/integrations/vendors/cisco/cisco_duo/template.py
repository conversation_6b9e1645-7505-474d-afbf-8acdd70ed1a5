from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CiscoDuoV1TemplateVersion


class CiscoDuoTemplate(Template):
    id = "cisco_duo"
    name = "Cisco Duo"
    category = Template.Category.IDENTITY_SECURITY
    vendor = Vendors.CISCO
    versions = {
        CiscoDuoV1TemplateVersion.id: CiscoDuoV1TemplateVersion(),
    }

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
