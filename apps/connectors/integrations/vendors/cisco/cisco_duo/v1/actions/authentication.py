from apps.connectors.integrations.schemas import ocsf


def map_factor_type(factor: str) -> ocsf.AuthenticationFactorType:
    """Map Cisco Duo factor to OCSF AuthenticationFactorType."""
    factor_mapping = {
        "duo_push": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
        "verified_duo_push": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
        "duo_mobile_passcode": ocsf.AuthenticationFactorType.OTP,
        "duo_mobile_passcode_hotp": ocsf.AuthenticationFactorType.OTP,
        "duo_mobile_passcode_totp": ocsf.AuthenticationFactorType.OTP,
        "hardware_token": ocsf.AuthenticationFactorType.HARDWARE_TOKEN,
        "sms_passcode": ocsf.AuthenticationFactorType.SMS,
        "sms_refresh": ocsf.AuthenticationFactorType.SMS,
        "phone_call": ocsf.AuthenticationFactorType.PHONE_CALL,
        "yubikey_code": ocsf.AuthenticationFactorType.OTP,
        "webauthn_credential": ocsf.AuthenticationFactorType.WEBAUTHN,
        "webauthn_security_key": ocsf.AuthenticationFactorType.WEBAUTHN,
        "not_available": ocsf.AuthenticationFactorType.UNKNOWN,
    }
    return factor_mapping.get(factor.lower(), ocsf.AuthenticationFactorType.OTHER)


def map_status_and_disposition(result: str, reason: str) -> tuple[ocsf.EventStatus, ocsf.Disposition]:
    """Map Cisco Duo result and reason to OCSF status and disposition."""
    if result == "success":
        return ocsf.EventStatus.SUCCESS, ocsf.Disposition.ALLOWED
    elif result == "denied":
        # Map specific denial reasons to dispositions based on documentation
        reason_mapping = {
            "user_marked_fraud": ocsf.Disposition.ALERT,
            "deny_unenrolled_user": ocsf.Disposition.UNAUTHORIZED,
            "error": ocsf.Disposition.ERROR,
            "locked_out": ocsf.Disposition.DELAYED,
            "user_disabled": ocsf.Disposition.ACCESS_REVOKED,
            "user_cancelled": ocsf.Disposition.DROPPED,
            "invalid_passcode": ocsf.Disposition.UNAUTHORIZED,
            "no_response": ocsf.Disposition.NO_ACTION,
            "no_keys_pressed": ocsf.Disposition.NO_ACTION,
            "call_timed_out": ocsf.Disposition.NO_ACTION,
            "location_restricted": ocsf.Disposition.BLOCKED,
            "factor_restricted": ocsf.Disposition.REJECTED,
            "platform_restricted": ocsf.Disposition.REJECTED,
            "version_restricted": ocsf.Disposition.REJECTED,
            "rooted_device": ocsf.Disposition.REJECTED,
            "no_screen_lock": ocsf.Disposition.REJECTED,
            "touch_id_disabled": ocsf.Disposition.REJECTED,
            "no_disk_encryption": ocsf.Disposition.REJECTED,
            "anonymous_ip": ocsf.Disposition.REJECTED,
            "out_of_date": ocsf.Disposition.REJECTED,
            "denied_by_policy": ocsf.Disposition.UNAUTHORIZED,
            "software_restricted": ocsf.Disposition.BLOCKED,
        }
        return ocsf.EventStatus.FAILURE, reason_mapping.get(reason, ocsf.Disposition.BLOCKED)
    elif result == "failure":
        return ocsf.EventStatus.FAILURE, ocsf.Disposition.ERROR
    elif result == "error":
        return ocsf.EventStatus.FAILURE, ocsf.Disposition.ERROR
    elif result == "fraud":
        return ocsf.EventStatus.FAILURE, ocsf.Disposition.ALERT
    else:
        return ocsf.EventStatus.UNKNOWN, ocsf.Disposition.UNKNOWN


def normalize_authentication_log(log: dict) -> ocsf.Authentication:
    """
    Normalize a Cisco Duo authentication log to OCSF Authentication format.
    
    Based on the detailed field mapping in Cisco_Duo_Data_Integration_Documentation.md
    """
    access_device = log.get("access_device", {})
    auth_device = log.get("auth_device", {})
    user_info = log.get("user", {})
    application = log.get("application", {})
    location = access_device.get("location", {})
    auth_device_location = auth_device.get("location", {})
    
    # Map status and disposition
    result = log.get("result", "")
    reason = log.get("reason", "")
    status, disposition = map_status_and_disposition(result, reason)
    
    # Map authentication factor
    factor = log.get("factor", "")
    factor_type = map_factor_type(factor)
    
    # Determine activity type based on event_type
    event_type = log.get("event_type", "")
    activity = ocsf.AuthenticationActivity.LOGON if event_type == "authentication" else ocsf.AuthenticationActivity.PREAUTH
    
    return ocsf.Authentication(
        activity=activity,
        time_dt=log.get("isotimestamp"),
        time=int(log.get("timestamp", 0)),
        metadata=ocsf.Metadata(
            uid=log.get("txid"),
            product=ocsf.Product(
                name="Cisco Duo",
                vendor_name="Cisco",
            ),
        ),
        actor=ocsf.Actor(
            user=ocsf.User(
                name=user_info.get("name"),
                uid=user_info.get("key"),
                uid_alt=log.get("alias") if log.get("alias") else None,
                email_addr=log.get("email"),
                groups=[ocsf.Group(name=group) for group in user_info.get("groups", [])],
            ),
        ),
        src_endpoint=ocsf.NetworkEndpoint(
            ip=access_device.get("ip"),
            hostname=access_device.get("hostname"),
            uid=access_device.get("epkey"),
            location=ocsf.GeoLocation(
                city=location.get("city"),
                country=location.get("country"),
                region=location.get("state"),
                lat=location.get("latitude"),
                long=location.get("longitude"),
            ) if location else None,
            os=ocsf.OperatingSystem(
                name=access_device.get("os"),
                version=access_device.get("os_version"),
            ) if access_device.get("os") else None,
        ) if access_device else None,
        device=ocsf.Device(
            name=auth_device.get("name"),
            uid=auth_device.get("key"),
            ip=auth_device.get("ip"),
            location=ocsf.GeoLocation(
                city=auth_device_location.get("city"),
                country=auth_device_location.get("country"),
                region=auth_device_location.get("state"),
                lat=auth_device_location.get("latitude"),
                long=auth_device_location.get("longitude"),
            ) if auth_device_location else None,
        ) if auth_device else None,
        service=ocsf.Service(
            name=application.get("name"),
            uid=application.get("key"),
        ) if application else None,
        auth_factors=[
            ocsf.AuthenticationFactor(
                factor_type=factor_type,
                provider="Cisco Duo",
            )
        ] if factor else [],
        status=status,
        disposition=disposition,
        status_detail=reason,
        http_request=ocsf.HttpRequest(
            user_agent=f"{access_device.get('browser', '')} {access_device.get('browser_version', '')}".strip()
        ) if access_device.get("browser") else None,
    )
