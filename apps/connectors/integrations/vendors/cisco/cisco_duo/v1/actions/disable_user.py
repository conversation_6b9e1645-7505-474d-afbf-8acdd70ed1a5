from apps.connectors.integrations.actions.user import (
    DisableU<PERSON><PERSON><PERSON><PERSON>,
    User<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import CiscoDuoV1Api
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllHosts,  # We'll create a proper user management health check later
)


class CiscoDuoV1DisableUser(DisableUserLogin):
    """
    Disable a user account in Cisco Duo.

    This action uses the Duo Admin API to set a user's status to 'disable'.
    Reference: https://duo.com/docs/adminapi#modify-user
    """

    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        """
        Execute the disable user action.

        Args:
            args: UserIdentifierArgs containing the user_id to disable

        Returns:
            UserLoginResult: Result indicating whether the user was disabled
        """
        api: CiscoDuoV1Api = self.integration.get_api()

        # Call the Duo API to disable the user
        response = api.modify_user(args.user_id.value, status="disable")

        # Check if the operation was successful
        if response.get("stat") == "OK":
            return UserLoginResult(result=UserLoginStatus(enabled=False))
        else:
            error_message = response.get("message", "Failed to disable user")
            return UserLoginResult(
                error=ErrorDetail(message=f"Failed to disable user: {error_message}")
            )

    def get_permission_checks(self):
        # TODO: Create proper user management health check
        return [ReadAllHosts]
