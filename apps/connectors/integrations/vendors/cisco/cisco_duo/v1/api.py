import base64
import email.utils
import hashlib
import hmac
import urllib

from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["response"]
    next_offset = response["metadata"].get("next_offset")
    offset = 0
    while next_offset:
        offset = offset + 1
        response = bound_method(**kwargs, offset=offset)
        if ("response" in response) and len(response["response"]) > 0:
            yield response["response"]
        else:
            break


class CiscoDuoV1Api(ApiBase):
    def __init__(self, ikey=None, skey=None, host=None, **kwargs):
        self.ikey = ikey
        self.skey = skey
        self.host = host

        # for post queries
        static_headers = {"Content-Type": "application/x-www-form-urlencoded"}
        url = f"https://api-{self.host}.duosecurity.com"
        super().__init__(base_url=url, static_headers=static_headers)

    def sign(self, method, path, params=None):
        """
        Return HTTP Basic Authentication ("Authorization" and "Date") headers.
        method, host, path: strings from request
        params: dict of request parameters
        skey: secret key
        ikey: integration key
        """
        # create canonical string
        now = email.utils.formatdate()
        canon = [now, method.upper(), self.host.lower(), path]
        args = []
        params = params or {}
        for key in sorted(params.keys()):
            val = repr(params[key]).encode("utf-8")
            args.append(
                "%s=%s" % (urllib.parse.quote(key, "~"), urllib.parse.quote(val, "~"))
            )
        canon.append("&".join(args))
        canon = "\n".join(canon)

        # sign canonical string
        sig = hmac.new(
            bytes(self.skey, encoding="utf-8"),
            bytes(canon, encoding="utf-8"),
            hashlib.sha1,
        )
        auth = "%s:%s" % (self.ikey, sig.hexdigest())

        # update headers
        self.session.headers.update(
            {
                "Date": now,
                "Authorization": "Basic %s"
                % base64.b64encode(bytes(auth, encoding="utf-8")).decode(),
            }
        )

    def get_endpoints(self, offset=0, limit=100):
        """
        Get a list of all endpoints. https://duo.com/docs/adminapi#endpoints
        """
        assert 1 < limit < 500

        url_path = "/admin/v1/endpoints"
        params = {"offset": offset, "limit": limit}

        self.sign("get", url_path, params)

        response = self.session.get(self.url(url_path), params=params)
        return response.json()

    def get_account_info(self):
        url_path = "/admin/v1/info/account"
        self.sign("get", url_path)
        response = self.session.get(self.url("/admin/v1/info/summary"))
        return response.json()

    def modify_user(self, user_id: str, status: str) -> dict:
        """
        Modify a user's status using the Duo Admin API.

        Args:
            user_id: The Duo user ID to modify
            status: The status to set ('disable', 'locked_out', 'active')

        Returns:
            dict: The API response

        Reference: https://duo.com/docs/adminapi#modify-user
        """
        url_path = f"/admin/v1/users/{user_id}"
        params = {"status": status}

        self.sign("post", url_path, params)

        response = self.session.post(self.url(url_path), data=params)
        response.raise_for_status()
        return response.json()

    def get_authentication_logs(self, users: str, mintime: int = None, maxtime: int = None,
                              limit: int = 1000, offset: int = 0) -> dict:
        """
        Get authentication logs for a specific user.

        Args:
            users: Comma-separated list of user IDs to filter by
            mintime: Unix timestamp for start of time range (optional)
            maxtime: Unix timestamp for end of time range (optional)
            limit: Maximum number of records to return (default: 1000, max: 1000)
            offset: Offset for pagination (default: 0)

        Returns:
            dict: The API response containing authentication logs

        Reference: https://duo.com/docs/adminapi#authentication-logs
        """
        url_path = "/admin/v1/logs/authentication"
        params = {
            "users": users,
            "limit": min(limit, 1000),  # Duo API max limit is 1000
            "offset": offset,
        }

        if mintime is not None:
            params["mintime"] = mintime
        if maxtime is not None:
            params["maxtime"] = maxtime

        self.sign("get", url_path, params)

        response = self.session.get(self.url(url_path), params=params)
        response.raise_for_status()
        return response.json()
