from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CiscoIseV1TemplateVersion


class CiscoIseTemplate(Template):
    id = "cisco_ise"
    name = "Cisco Identity Services Engine (ISE)"
    category = Template.Category.ASSET_SOURCE
    vendor = Vendors.CISCO
    versions = {
        CiscoIseV1TemplateVersion.id: CiscoIseV1TemplateVersion(),
    }

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
