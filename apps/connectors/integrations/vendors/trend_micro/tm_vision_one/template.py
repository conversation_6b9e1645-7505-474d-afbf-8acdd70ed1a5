from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import TmVisionOneV1TemplateVersion


class TmVisionOneTemplate(Template):
    id = "tm_vision_one"
    name = "Trend Micro Vision One"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        TmVisionOneV1TemplateVersion.id: TmVisionOneV1TemplateVersion(),
    }
    vendor = Vendors.TREND_MICRO
    endpoint_coverage_available = True

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
