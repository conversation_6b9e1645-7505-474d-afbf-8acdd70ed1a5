from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdiV1TemplateVersion


class MsMdiTemplate(Template):
    id = "ms_mdi"
    name = "Microsoft Defender for Identity"
    category = Template.Category.IDENTITY_SECURITY
    versions = {
        MsMdiV1TemplateVersion.id: MsMdiV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
