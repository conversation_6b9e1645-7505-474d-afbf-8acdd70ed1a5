from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class AzureKeyVaultTemplate(Template):
    id = "azure_key_vault"
    name = "Microsoft Azure Key Vault"
    category = Template.Category.CLOUD_SECURITY
    versions = {}
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
