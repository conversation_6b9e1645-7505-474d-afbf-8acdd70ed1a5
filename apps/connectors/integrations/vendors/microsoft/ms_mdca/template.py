from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdcaV1TemplateVersion


class MsMdcaTemplate(Template):
    id = "ms_mdca"
    name = "Microsoft Defender for Cloud Apps"
    category = Template.Category.SAAS_SECURITY
    versions = {
        MsMdcaV1TemplateVersion.id: MsMdcaV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
