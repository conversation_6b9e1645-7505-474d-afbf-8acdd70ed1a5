from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdoV1TemplateVersion


class MsMdoTemplate(Template):
    id = "ms_mdo"
    name = "Microsoft Defender for Office"
    category = Template.Category.EMAIL_SECURITY
    versions = {
        MsMdoV1TemplateVersion.id: MsMdoV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
