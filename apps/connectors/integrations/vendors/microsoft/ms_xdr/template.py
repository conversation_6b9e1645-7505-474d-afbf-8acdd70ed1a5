from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsXdrV1TemplateVersion


class MsXdrTemplate(Template):
    id = "ms_xdr"
    name = "Microsoft Defender XDR"
    category = Template.Category.MDR_SUPPORTING
    versions = {
        MsXdrV1TemplateVersion.id: MsXdrV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
