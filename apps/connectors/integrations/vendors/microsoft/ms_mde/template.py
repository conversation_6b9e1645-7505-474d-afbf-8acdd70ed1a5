from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdeV1TemplateVersion


class MsMdeTemplate(Template):
    id = "ms_mde"
    name = "Microsoft Defender for Endpoint (new)"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        MsMdeV1TemplateVersion.id: MsMdeV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
