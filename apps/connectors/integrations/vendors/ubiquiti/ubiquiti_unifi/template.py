from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import UbiquitiUnifiV1TemplateVersion


class UbiquitiUnifiTemplate(Template):
    id = "ubiquiti_unifi"
    name = "Ubiquiti UniFi"
    category = Template.Category.ASSET_SOURCE
    versions = {
        UbiquitiUnifiV1TemplateVersion.id: UbiquitiUnifiV1TemplateVersion(),
    }
    vendor = Vendors.UBIQUITI
