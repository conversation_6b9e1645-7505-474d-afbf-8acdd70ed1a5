from unificontrol import UnifiTransportError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.list_devices()
            return IntegrationHealthCheckResult.PASSED
        except UnifiTransportError:
            return IntegrationHealthCheckResult.FAILED


class ReadDeviceInventory(IntegrationPermissionsHealthCheck):
    name = "Read devices inventory"
    description = "Read devices inventory from Ubiquiti Unifi."
    value = "devices_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        # We already test list devices is the connection health check,
        # so we can just return passed here.
        return IntegrationHealthCheckResult.PASSED
        # try:
        #     self.integration.invoke("list_devices")
        #     return IntegrationHealthCheckResult.PASSED
        # except UnifiTransportError:
        #     return IntegrationHealthCheckResult.FAILED
