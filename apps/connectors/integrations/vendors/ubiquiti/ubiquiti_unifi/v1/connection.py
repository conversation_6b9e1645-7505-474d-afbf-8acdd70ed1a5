from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class UbiquitiUnifiV1Config(TemplateVersionConfig):
    host: str = Field(
        title="Hostname",
        description="Hostname of the UniFi controller",
    )
    username: str = Field(
        title="Username",
        description="Username for the UniFi controller",
    )
    password: EncryptedStr = Field(
        title="Password",
        description="Password for the UniFi controller",
    )
    site: str = Field(
        title="Site",
        description="Site name for the UniFi controller",
    )


class UbiquitiUnifiV1Connection(ConnectionTemplate):
    id = "ubiquiti_unifi"
    name = "Ubiquiti Unifi"
    config_model = UbiquitiUnifiV1Config
