from unificontrol import UnifiClient


class UbiquitiUnifiV1Api:
    def __init__(self, host=None, username=None, password=None, site=None):
        self.host = host
        self.username = username
        self.password = password
        self.site = site
        self.client = UnifiClient(
            host=self.host,
            username=self.username,
            password=self.password,
            site=self.site,
            cert=None,
        )

    def list_devices(self):
        return self.client.list_devices()
