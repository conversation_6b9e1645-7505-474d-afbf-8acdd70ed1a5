from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v2_1 import SentinelOneV21TemplateVersion


class SentinelOneTemplate(Template):
    id = "sentinel_one"
    name = "SentinelOne"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        SentinelOneV21TemplateVersion.id: SentinelOneV21TemplateVersion(),
    }
    vendor = Vendors.SENTINELONE
    endpoint_coverage_available = True

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
