from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CrowdstrikeNgSiemV1TemplateVersion


class CrowdstrikeNgSiemTemplate(Template):
    id = "crowdstrike_ng_siem"
    name = "CrowdStrike Falcon Next-Gen SIEM"
    category = Template.Category.SIEM
    versions = {
        CrowdstrikeNgSiemV1TemplateVersion.id: CrowdstrikeNgSiemV1TemplateVersion(),
    }
    vendor = Vendors.CROWDSTRIKE

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True
