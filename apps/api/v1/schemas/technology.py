from typing import List, Optional

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
)

from apps.connectors.integrations import IntegrationActionType


class TechnologyBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    category_name: str
    vendor_id: str
    vendor_name: str
    vulnerability_coverage_available: bool
    endpoint_coverage_available: bool
    is_alert_source: bool


class TechnologySummary(TechnologyBase):
    model_config = ConfigDict(from_attributes=True)

    version_ids: List[str] = Field(validation_alias="versions")

    @field_validator("version_ids", mode="before")
    def get_versions_ids(cls, versions):
        return list(versions.keys())


class JSONSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    defs: Optional[dict] = Field(alias="$defs", default={})
    properties: dict = Field(default={})
    required: List[str] = Field(default_factory=list)
    title: str
    type: str


class TechnologyConfig(JSONSchema):
    pass


class TechnologySettings(JSONSchema):
    pass


class ActionMetadata(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    entitlement: str
    settings: TechnologySettings


class TechnologyVersion(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    supported_actions: List[IntegrationActionType] = Field(
        validation_alias="visible_supported_actions"
    )
    connection_template_id: str = None
    connection_config: TechnologyConfig = Field(
        validation_alias="connection_config_model",
        default=None,
    )
    config: TechnologyConfig = Field(validation_alias="connection_model")
    settings: TechnologySettings = Field(validation_alias="settings_model")
    actions: dict[IntegrationActionType, ActionMetadata] = Field(
        validation_alias="actions_metadata"
    )

    @field_validator("config", mode="before")
    def get_config(cls, config):
        return config.config_model.model_json_schema()

    @field_validator("settings", mode="before")
    def get_settings(cls, settings):
        return settings.model_json_schema()

    @field_validator("actions", mode="before")
    def get_actions_metadata(cls, actions):
        return {
            action: metadata
            for action, metadata in actions.items()
            if action in IntegrationActionType._value2member_map_
        }

    @field_validator("supported_actions", mode="before")
    def get_supported_actions(cls, supported_actions):
        return [
            action
            for action in supported_actions
            if action in IntegrationActionType._value2member_map_
        ]


class Technology(TechnologyBase):
    model_config = ConfigDict(from_attributes=True)

    versions: List[TechnologyVersion]
